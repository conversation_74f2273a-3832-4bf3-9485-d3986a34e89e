<template>
  <v-container class="pa-6" fluid>
    <!-- Welcome Header -->
    <v-row class="mb-6">
      <v-col>
        <div class="d-flex align-center justify-space-between">
          <div>
            <h1 class="text-h3 font-weight-bold mb-2">
              Welcome back, {{ authStore.currentUser?.name || 'User' }}!
            </h1>
            <p class="text-subtitle-1 text-medium-emphasis">
              Here's an overview of your tasks and projects
            </p>
          </div>
          <div class="d-flex align-center ga-2">
            <v-btn
              color="primary"
              prepend-icon="mdi-plus"
              variant="elevated"
              @click="$router.push('/tasks/new')"
            >
              Add Task
            </v-btn>
            <v-btn
              color="secondary"
              prepend-icon="mdi-folder-plus"
              variant="elevated"
              @click="$router.push('/projects')"
            >
              New Project
            </v-btn>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Dashboard Stats -->
    <DashboardStats />

    <!-- Quick Actions -->
    <v-row class="mb-6">
      <v-col>
        <h2 class="text-h5 font-weight-bold mb-4">Quick Actions</h2>
        <v-row>
          <v-col cols="12" sm="6" md="3">
            <v-card class="text-center pa-4" hover @click="$router.push('/')">
              <v-icon size="48" color="primary" class="mb-2">mdi-format-list-checks</v-icon>
              <v-card-title class="text-h6">My Tasks</v-card-title>
              <v-card-text>View and manage all your tasks</v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-card class="text-center pa-4" hover @click="$router.push('/projects')">
              <v-icon size="48" color="secondary" class="mb-2">mdi-folder-multiple</v-icon>
              <v-card-title class="text-h6">Projects</v-card-title>
              <v-card-text>Organize tasks by projects</v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-card class="text-center pa-4" hover @click="$router.push('/upload')">
              <v-icon size="48" color="success" class="mb-2">mdi-file-upload</v-icon>
              <v-card-title class="text-h6">Import Tasks</v-card-title>
              <v-card-text>Upload tasks from files</v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" sm="6" md="3">
            <v-card class="text-center pa-4" hover @click="$router.push('/profile')">
              <v-icon size="48" color="info" class="mb-2">mdi-account-settings</v-icon>
              <v-card-title class="text-h6">Profile</v-card-title>
              <v-card-text>Manage your account</v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <!-- Recent Activity -->
    <v-row>
      <v-col cols="12" md="8">
        <v-card>
          <v-card-title class="d-flex align-center justify-space-between">
            <span>Recent Tasks</span>
            <v-btn variant="text" size="small" @click="$router.push('/')">
              View All
            </v-btn>
          </v-card-title>
          <v-card-text>
            <div v-if="recentTasks.length === 0" class="text-center py-8">
              <v-icon size="64" color="grey-lighten-1" class="mb-4">mdi-clipboard-text-outline</v-icon>
              <p class="text-h6 text-medium-emphasis">No recent tasks</p>
              <p class="text-body-2 text-medium-emphasis mb-4">
                Create your first task to get started
              </p>
              <v-btn color="primary" @click="$router.push('/tasks/new')">
                Create Task
              </v-btn>
            </div>
            <v-list v-else>
              <v-list-item
                v-for="task in recentTasks.slice(0, 5)"
                :key="task.id"
                @click="$router.push(`/tasks/${task.id}`)"
              >
                <template #prepend>
                  <v-icon :color="getPriorityColor(task.priority)">
                    {{ getPriorityIcon(task.priority) }}
                  </v-icon>
                </template>
                <v-list-item-title>{{ task.title }}</v-list-item-title>
                <v-list-item-subtitle>{{ task.project_name || 'No Project' }}</v-list-item-subtitle>
                <template #append>
                  <v-chip :color="getStatusColor(task.status)" size="small">
                    {{ task.status }}
                  </v-chip>
                </template>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" md="4">
        <v-card>
          <v-card-title>Projects Overview</v-card-title>
          <v-card-text>
            <div v-if="projectsStore.projects.length === 0" class="text-center py-4">
              <v-icon size="48" color="grey-lighten-1" class="mb-2">mdi-folder-outline</v-icon>
              <p class="text-body-2 text-medium-emphasis mb-4">No projects yet</p>
              <v-btn color="secondary" size="small" @click="$router.push('/projects')">
                Create Project
              </v-btn>
            </div>
            <v-list v-else>
              <v-list-item
                v-for="project in projectsStore.projects.slice(0, 3)"
                :key="project.id"
                @click="$router.push('/projects')"
              >
                <template #prepend>
                  <v-icon color="secondary">mdi-folder</v-icon>
                </template>
                <v-list-item-title>{{ project.name }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ getProjectTaskCount(project.id) }} tasks
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useTasksStore } from '@/stores/tasks'
import { useProjectsStore } from '@/stores/projects'
import DashboardStats from '@/components/dashboard/DashboardStats.vue'

// Stores
const authStore = useAuthStore()
const tasksStore = useTasksStore()
const projectsStore = useProjectsStore()

// Computed
const recentTasks = computed(() => {
  return tasksStore.tasks
    .slice()
    .sort((a, b) => new Date(b.updated_at || b.created_at) - new Date(a.updated_at || a.created_at))
})

// Methods
const getPriorityColor = (priority) => {
  const colors = {
    'high': 'error',
    'medium': 'warning',
    'low': 'success'
  }
  return colors[priority] || 'grey'
}

const getPriorityIcon = (priority) => {
  const icons = {
    'high': 'mdi-arrow-up',
    'medium': 'mdi-minus',
    'low': 'mdi-arrow-down'
  }
  return icons[priority] || 'mdi-minus'
}

const getStatusColor = (status) => {
  const colors = {
    'todo': 'grey',
    'in_progress': 'primary',
    'completed': 'success',
    'blocked': 'error'
  }
  return colors[status] || 'grey'
}

const getProjectTaskCount = (projectId) => {
  return tasksStore.tasks.filter(task => task.project_id === projectId).length
}

// Lifecycle
onMounted(async () => {
  // Only fetch data if user is authenticated
  if (authStore.isAuthenticated) {
    await Promise.all([
      projectsStore.fetchProjects(),
      tasksStore.fetchTasks(),
    ])
  }
})
</script>

<style scoped>
.v-card {
  transition: all 0.3s ease;
}

.v-card:hover {
  transform: translateY(-2px);
}
</style>
