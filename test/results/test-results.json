{"numTotalTestSuites": 7, "numPassedTestSuites": 7, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 35, "numPassedTests": 35, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753566835117, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from / to login", "status": "passed", "title": "should redirect unauthenticated users from / to login", "duration": 6.360765999999785, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /", "status": "passed", "title": "should allow authenticated users to access /", "duration": 0.5823860000000423, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /dashboard to login", "status": "passed", "title": "should redirect unauthenticated users from /dashboard to login", "duration": 0.770170000001599, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /dashboard", "status": "passed", "title": "should allow authenticated users to access /dashboard", "duration": 0.4428220000008878, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /projects to login", "status": "passed", "title": "should redirect unauthenticated users from /projects to login", "duration": 0.5645430000004126, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /projects", "status": "passed", "title": "should allow authenticated users to access /projects", "duration": 0.40567899999950896, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /tasks to login", "status": "passed", "title": "should redirect unauthenticated users from /tasks to login", "duration": 0.5145739999989019, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /tasks", "status": "passed", "title": "should allow authenticated users to access /tasks", "duration": 0.4936200000011013, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /tasks/123 to login", "status": "passed", "title": "should redirect unauthenticated users from /tasks/123 to login", "duration": 0.7894840000008116, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /tasks/123", "status": "passed", "title": "should allow authenticated users to access /tasks/123", "duration": 0.4274310000000696, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /upload to login", "status": "passed", "title": "should redirect unauthenticated users from /upload to login", "duration": 0.39929599999959464, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /upload", "status": "passed", "title": "should allow authenticated users to access /upload", "duration": 0.2550780000001396, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /profile to login", "status": "passed", "title": "should redirect unauthenticated users from /profile to login", "duration": 0.3170509999999922, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /profile", "status": "passed", "title": "should allow authenticated users to access /profile", "duration": 0.24112500000046566, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should redirect unauthenticated users from /settings to login", "status": "passed", "title": "should redirect unauthenticated users from /settings to login", "duration": 0.2825380000012956, "failureMessages": [], "location": {"line": 109, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Protected Routes"], "fullName": "Authentication Guards Integration Tests Protected Routes should allow authenticated users to access /settings", "status": "passed", "title": "should allow authenticated users to access /settings", "duration": 0.22639600000002247, "failureMessages": [], "location": {"line": 125, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /login", "status": "passed", "title": "should allow unauthenticated users to access /login", "duration": 0.3006789999999455, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /login to dashboard", "status": "passed", "title": "should redirect authenticated users from /login to dashboard", "duration": 0.3424719999984518, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /login to original destination", "status": "passed", "title": "should redirect authenticated users from /login to original destination", "duration": 0.9583899999997811, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /register", "status": "passed", "title": "should allow unauthenticated users to access /register", "duration": 0.30310999999892374, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /register to dashboard", "status": "passed", "title": "should redirect authenticated users from /register to dashboard", "duration": 0.2823659999994561, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /register to original destination", "status": "passed", "title": "should redirect authenticated users from /register to original destination", "duration": 0.3581799999992654, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /forgot-password", "status": "passed", "title": "should allow unauthenticated users to access /forgot-password", "duration": 0.23920100000032107, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /forgot-password to dashboard", "status": "passed", "title": "should redirect authenticated users from /forgot-password to dashboard", "duration": 0.24073199999838835, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /forgot-password to original destination", "status": "passed", "title": "should redirect authenticated users from /forgot-password to original destination", "duration": 0.33801800000037474, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should allow unauthenticated users to access /reset-password", "status": "passed", "title": "should allow unauthenticated users to access /reset-password", "duration": 0.23986499999955413, "failureMessages": [], "location": {"line": 144, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /reset-password to dashboard", "status": "passed", "title": "should redirect authenticated users from /reset-password to dashboard", "duration": 0.24189099999966857, "failureMessages": [], "location": {"line": 157, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Guest-Only Routes"], "fullName": "Authentication Guards Integration Tests Guest-Only Routes should redirect authenticated users from /reset-password to original destination", "status": "passed", "title": "should redirect authenticated users from /reset-password to original destination", "duration": 0.3567299999995157, "failureMessages": [], "location": {"line": 170, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Public Routes"], "fullName": "Authentication Guards Integration Tests Public Routes should allow unauthenticated users to access /help", "status": "passed", "title": "should allow unauthenticated users to access /help", "duration": 0.3319850000007136, "failureMessages": [], "location": {"line": 189, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Public Routes"], "fullName": "Authentication Guards Integration Tests Public Routes should allow authenticated users to access /help", "status": "passed", "title": "should allow authenticated users to access /help", "duration": 0.3115490000000136, "failureMessages": [], "location": {"line": 202, "column": 9}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should initialize auth store if not initialized", "status": "passed", "title": "should initialize auth store if not initialized", "duration": 0.4747690000003786, "failureMessages": [], "location": {"line": 218, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should handle auth initialization failure gracefully", "status": "passed", "title": "should handle auth initialization failure gracefully", "duration": 0.5337120000003779, "failureMessages": [], "location": {"line": 231, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Authentication Initialization"], "fullName": "Authentication Guards Integration Tests Authentication Initialization should not initialize auth store if already initialized", "status": "passed", "title": "should not initialize auth store if already initialized", "duration": 0.49165800000082527, "failureMessages": [], "location": {"line": 245, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Redirect Preservation"], "fullName": "Authentication Guards Integration Tests Redirect Preservation should preserve redirect query parameter when redirecting to login", "status": "passed", "title": "should preserve redirect query parameter when redirecting to login", "duration": 1.2427049999987503, "failureMessages": [], "location": {"line": 261, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Guards Integration Tests", "Redirect Preservation"], "fullName": "Authentication Guards Integration Tests Redirect Preservation should handle complex paths with query parameters", "status": "passed", "title": "should handle complex paths with query parameters", "duration": 0.6913280000007944, "failureMessages": [], "location": {"line": 277, "column": 7}, "meta": {}}], "startTime": 1753566846441, "endTime": 1753566846462.6914, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/test/integration/auth-guards.test.js"}]}