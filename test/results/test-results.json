{"numTotalTestSuites": 11, "numPassedTestSuites": 11, "numFailedTestSuites": 0, "numPendingTestSuites": 0, "numTotalTests": 17, "numPassedTests": 17, "numFailedTests": 0, "numPendingTests": 0, "numTodoTests": 0, "snapshot": {"added": 0, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0, "didUpdate": false}, "startTime": 1753574189974, "success": true, "testResults": [{"assertionResults": [{"ancestorTitles": ["Authentication Store (TEST-001)", "Initial State"], "fullName": "Authentication Store (TEST-001) Initial State should initialize with correct defaults", "status": "passed", "title": "should initialize with correct defaults", "duration": 5.761817000000519, "failureMessages": [], "location": {"line": 49, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return correct isLoggedIn status", "status": "passed", "title": "should return correct isLoggedIn status", "duration": 1.8031630000004952, "failureMessages": [], "location": {"line": 61, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Getters"], "fullName": "Authentication Store (TEST-001) Getters should return current user", "status": "passed", "title": "should return current user", "duration": 1.6030890000001818, "failureMessages": [], "location": {"line": 70, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should login successfully", "status": "passed", "title": "should login successfully", "duration": 3.4260459999995874, "failureMessages": [], "location": {"line": 79, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login failure", "status": "passed", "title": "should handle login failure", "duration": 1.2270739999994476, "failureMessages": [], "location": {"line": 109, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Login Action"], "fullName": "Authentication Store (TEST-001) Login Action should handle login exception", "status": "passed", "title": "should handle login exception", "duration": 0.8419589999994059, "failureMessages": [], "location": {"line": 129, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should register successfully", "status": "passed", "title": "should register successfully", "duration": 0.9850109999997585, "failureMessages": [], "location": {"line": 148, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Register Action"], "fullName": "Authentication Store (TEST-001) Register Action should handle registration failure", "status": "passed", "title": "should handle registration failure", "duration": 0.5295709999991232, "failureMessages": [], "location": {"line": 173, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Logout Action"], "fullName": "Authentication Store (TEST-001) Logout Action should logout successfully", "status": "passed", "title": "should logout successfully", "duration": 1.1861369999996896, "failureMessages": [], "location": {"line": 192, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should refresh token successfully", "status": "passed", "title": "should refresh token successfully", "duration": 2.0834160000003976, "failureMessages": [], "location": {"line": 214, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Token Refresh"], "fullName": "Authentication Store (TEST-001) Token Refresh should handle token refresh failure", "status": "passed", "title": "should handle token refresh failure", "duration": 0.82649500000025, "failureMessages": [], "location": {"line": 247, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should request password reset successfully", "status": "passed", "title": "should request password reset successfully", "duration": 0.8644459999995888, "failureMessages": [], "location": {"line": 259, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Password Reset"], "fullName": "Authentication Store (TEST-001) Password Reset should reset password successfully", "status": "passed", "title": "should reset password successfully", "duration": 0.8480950000002849, "failureMessages": [], "location": {"line": 273, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should persist auth state to localStorage", "status": "passed", "title": "should persist auth state to localStorage", "duration": 0.6897150000004331, "failureMessages": [], "location": {"line": 293, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should restore auth state from localStorage", "status": "passed", "title": "should restore auth state from localStorage", "duration": 0.7826329999998052, "failureMessages": [], "location": {"line": 307, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "State Persistence"], "fullName": "Authentication Store (TEST-001) State Persistence should clear auth state", "status": "passed", "title": "should clear auth state", "duration": 0.7551599999997052, "failureMessages": [], "location": {"line": 324, "column": 7}, "meta": {}}, {"ancestorTitles": ["Authentication Store (TEST-001)", "Initialization"], "fullName": "Authentication Store (TEST-001) Initialization should initialize auth state", "status": "passed", "title": "should initialize auth state", "duration": 0.9604990000007092, "failureMessages": [], "location": {"line": 342, "column": 7}, "meta": {}}], "startTime": 1753574192516, "endTime": 1753574192542.9604, "status": "passed", "message": "", "name": "/Volumes/External Drive/Development/track-tasks/ui/stores/__tests__/auth.spec.js"}]}